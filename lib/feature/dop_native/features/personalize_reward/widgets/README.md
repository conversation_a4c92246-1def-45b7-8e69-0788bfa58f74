# Expandable Reward List Widgets

This folder contains widgets for creating an expandable list with smooth animations for the personalize reward screen. The list supports single-item selection and expansion.

## Components

### 1. `ExpandableRewardListItemModel`
Data model representing each list item with:
- `id`: Unique identifier
- `title`: Display title
- `description`: Expandable description text
- `logoIcons`: List of logo asset paths
- `isExpanded`: Current expansion state
- `isSelected`: Current selection state

### 2. `ExpandableRewardListItemWidget`
Individual list item widget featuring:
- Smooth expand/collapse animation
- Radio button selection
- Logo icons display (max 6 visible + more indicator)
- Tap to expand functionality
- Customizable animation duration

### 3. `ExpandableRewardListWidget`
Main list widget that manages:
- Multiple expandable items
- Single-item selection and expansion (selected item is automatically expanded)
- Simplified state management with one selectedItemId
- Scroll physics configuration
- Callback handling

### 4. `DOPNativePersonalizeRewardListWidget`
Production-ready widget for the personalize reward screen:
- Integrates with DOEPersonalizeRewardCategory data structure
- Handles loading and empty states
- Connects directly with DOPNativePersonalizeRewardCubit
- Optimized for the actual screen implementation

## Usage

### Basic Implementation

```dart
import 'package:flutter/material.dart';
import 'widgets/widgets.dart';

class MyRewardScreen extends StatefulWidget {
  @override
  State<MyRewardScreen> createState() => _MyRewardScreenState();
}

class _MyRewardScreenState extends State<MyRewardScreen> {
  String? selectedItemId;
  
  final List<ExpandableRewardListItemModel> items = [
    ExpandableRewardListItemModel(
      id: 'shopping',
      title: 'Mua sắm trực tuyến',
      description: 'Nhận ưu đãi khi mua sắm...',
      logoIcons: ['assets/logo1.png', 'assets/logo2.png'],
    ),
    // Add more items...
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ExpandableRewardListWidget(
        items: items,
        selectedItemId: selectedItemId,
        onItemSelected: (itemId) {
          setState(() {
            selectedItemId = itemId;
          });
        },
      ),
    );
  }
}
```

### Production Usage with DOEPersonalizeRewardCategory

```dart
// Use the production widget in DOPNativePersonalizeRewardScreen
DOPNativePersonalizeRewardListWidget(
  categories: categories, // List<DOEPersonalizeRewardCategory>
  selectedIndex: selectedIndex,
  isLoading: isLoading,
  onCategorySelected: cubit.onSelectReward,
)
```

## Features

### Animation
- Smooth expand/collapse with `SizeTransition`
- Configurable animation duration (default: 300ms)
- Curved animation with `Curves.easeInOut`
- Rotating arrow indicator

### Selection and Expansion
- Single selection with radio buttons
- Selected item is automatically expanded
- Simplified state management with one variable
- Visual feedback for selected state
- Callback for selection changes

### Logo Display
- Shows up to 6 logos in a row
- "+N" indicator for additional logos
- Error handling for missing images
- Consistent sizing and spacing

### Styling
- Follows DOP Native design system
- Consistent with existing app patterns
- Responsive layout
- Shadow and border radius

## Customization

### Animation Duration
```dart
ExpandableRewardListWidget(
  animationDuration: Duration(milliseconds: 500),
  // ...
)
```

### Scroll Physics
```dart
ExpandableRewardListWidget(
  physics: BouncingScrollPhysics(),
  // ...
)
```

### Shrink Wrap
```dart
ExpandableRewardListWidget(
  shrinkWrap: true,
  // ...
)
```

## Integration with Existing Code

The widgets are designed to integrate seamlessly with the existing DOP Native architecture:

1. **State Management**: Compatible with BLoC pattern
2. **Styling**: Uses `dopNativeTextStyles` and `dopNativeColors`
3. **Components**: Reuses `DOPNativeRadioButtonWidget`
4. **Data Models**: Works with `DOEPersonalizeRewardCategory`

## Key Changes

- **Simplified Interface**: Only one `selectedItemId` instead of separate `selectedItemId` and `expandedItemId`
- **Automatic Expansion**: When an item is selected, it automatically expands
- **Unified Interaction**: Tapping anywhere on an item selects and expands it
- **Cleaner State Management**: Easier to manage state with fewer variables

## Performance Considerations

- Efficient list rendering with `ListView.builder`
- Minimal rebuilds with proper state management
- Optimized animations with `SingleTickerProviderStateMixin`
- Image caching for logo assets
