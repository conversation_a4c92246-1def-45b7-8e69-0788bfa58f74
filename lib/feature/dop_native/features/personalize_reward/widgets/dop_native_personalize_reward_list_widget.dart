import 'package:flutter/material.dart';

import '../../../../../data/response/dop_native/dop_native_personalize_reward_entity.dart';
import '../../../resources/dop_native_resources.dart';
import 'expandable_reward_list_item_model.dart';
import 'expandable_reward_list_widget.dart';

/// Widget specifically designed for DOPNativePersonalizeRewardScreen
/// Integrates with DOEPersonalizeRewardCategory data and cubit
class DOPNativePersonalizeRewardListWidget extends StatefulWidget {
  /// List of reward categories from API
  final List<DOEPersonalizeRewardCategory> categories;

  /// Currently selected category index
  final int? selectedIndex;

  /// Callback when a category is selected
  final void Function(int index)? onCategorySelected;

  /// Whether to show loading state
  final bool isLoading;

  const DOPNativePersonalizeRewardListWidget({
    required this.categories,
    this.selectedIndex,
    this.onCategorySelected,
    this.isLoading = false,
    super.key,
  });

  @override
  State<DOPNativePersonalizeRewardListWidget> createState() =>
      _DOPNativePersonalizeRewardListWidgetState();
}

class _DOPNativePersonalizeRewardListWidgetState
    extends State<DOPNativePersonalizeRewardListWidget> {
  String? expandedItemId;

  late List<ExpandableRewardListItemModel> items;

  @override
  void initState() {
    super.initState();
    items = _convertFromCategories(widget.categories);
  }

  @override
  void didUpdateWidget(DOPNativePersonalizeRewardListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.categories != widget.categories) {
      items = _convertFromCategories(widget.categories);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return _buildLoadingState();
    }

    if (items.isEmpty) {
      return _buildEmptyState();
    }

    return ExpandableRewardListWidget(
      items: items,
      selectedItemId:
          widget.selectedIndex != null ? widget.categories[widget.selectedIndex!].code : null,
      expandedItemId: expandedItemId,
      onItemSelected: _handleItemSelected,
      onItemExpanded: _handleItemExpanded,
      physics: const BouncingScrollPhysics(),
      shrinkWrap: true,
    );
  }

  /// Convert DOEPersonalizeRewardCategory list to ExpandableRewardListItemModel list
  List<ExpandableRewardListItemModel> _convertFromCategories(
    List<DOEPersonalizeRewardCategory> categories,
  ) {
    return categories.map((DOEPersonalizeRewardCategory category) {
      return ExpandableRewardListItemModel(
        id: category.code ?? '',
        title: category.name ?? 'Unknown Category',
        description: _buildDescription(category),
        logoIcons: category.logo ?? [],
      );
    }).toList();
  }

  /// Build description from category data
  String _buildDescription(DOEPersonalizeRewardCategory category) {
    // Use shortDesc if available
    if (category.shortDesc != null && category.shortDesc!.isNotEmpty) {
      return category.shortDesc!.join('\n');
    }

    // Fallback to desc sections
    if (category.desc != null && category.desc!.isNotEmpty) {
      final StringBuffer buffer = StringBuffer();
      for (final desc in category.desc!) {
        if (desc.section != null && desc.section!.isNotEmpty) {
          buffer.writeln(desc.section);
        }
        if (desc.content != null && desc.content!.isNotEmpty) {
          buffer.writeln(desc.content!.join('\n'));
        }
      }
      return buffer.toString().trim();
    }

    return 'No description available for this category.';
  }

  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(
              color: dopNativeColors.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'Đang tải danh mục ưu đãi...',
              style: dopNativeTextStyles.bodySmall(
                color: dopNativeColors.textPassive,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.category_outlined,
              size: 64,
              color: dopNativeColors.textPassive,
            ),
            const SizedBox(height: 16),
            Text(
              'Không có danh mục ưu đãi',
              style: dopNativeTextStyles.h300(
                color: dopNativeColors.textActive,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Vui lòng thử lại sau',
              style: dopNativeTextStyles.bodySmall(
                color: dopNativeColors.textPassive,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleItemSelected(String itemId) {
    // Find the index of the selected category
    final int index = widget.categories.indexWhere((category) => category.code == itemId);
    if (index != -1) {
      widget.onCategorySelected?.call(index);
    }
  }

  void _handleItemExpanded(String itemId, bool isExpanded) {
    setState(() {
      expandedItemId = isExpanded ? itemId : null;
    });
  }
}

/// Utility class for working with DOEPersonalizeRewardCategory
class DOPNativePersonalizeRewardUtils {
  /// Validate that a category has required data for display
  static bool isValidCategory(DOEPersonalizeRewardCategory category) {
    return category.code != null &&
        category.code!.isNotEmpty &&
        category.name != null &&
        category.name!.isNotEmpty;
  }

  /// Filter categories to only include valid ones
  static List<DOEPersonalizeRewardCategory> filterValidCategories(
    List<DOEPersonalizeRewardCategory> categories,
  ) {
    return categories.where(isValidCategory).toList();
  }

  /// Get category by code
  static DOEPersonalizeRewardCategory? getCategoryByCode(
    List<DOEPersonalizeRewardCategory> categories,
    String code,
  ) {
    try {
      return categories.firstWhere((category) => category.code == code);
    } catch (e) {
      return null;
    }
  }

  /// Create sample data for testing
  static List<DOEPersonalizeRewardCategory> createSampleCategories() {
    return [
      const DOEPersonalizeRewardCategory(
        code: 'shopping_online',
        name: 'Mua sắm trực tuyến',
        logo: [
          'assets/images/logos/shopee.png',
          'assets/images/logos/lazada.png',
          'assets/images/logos/tiki.png',
          'assets/images/logos/sendo.png',
        ],
        shortDesc: [
          'Nhận ưu đãi khi mua sắm trực tuyến tại các trang thương mại điện tử hàng đầu.',
          'Tích lũy điểm thưởng và nhận cashback cho mỗi giao dịch.',
        ],
      ),
      const DOEPersonalizeRewardCategory(
        code: 'dining_entertainment',
        name: 'Ẩm thực & Giải trí',
        logo: [
          'assets/images/logos/grab.png',
          'assets/images/logos/gojek.png',
          'assets/images/logos/baemin.png',
          'assets/images/logos/now.png',
        ],
        shortDesc: [
          'Thưởng thức các món ăn ngon với ưu đãi đặc biệt tại hàng trăm nhà hàng.',
          'Giảm giá lên đến 30% cho các bữa ăn và đồ uống.',
        ],
      ),
    ];
  }
}
