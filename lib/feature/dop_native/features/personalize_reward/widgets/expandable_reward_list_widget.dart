import 'package:flutter/material.dart';

import 'expandable_reward_list_item_model.dart';
import 'expandable_reward_list_item_widget.dart';

/// Main expandable list widget that manages multiple expandable items
/// Only one item can be expanded at a time
class ExpandableRewardListWidget extends StatefulWidget {
  /// List of items to display
  final List<ExpandableRewardListItemModel> items;

  /// Callback when an item is selected (radio button tapped)
  final void Function(String itemId)? onItemSelected;

  /// Callback when an item is expanded/collapsed
  final void Function(String itemId, bool isExpanded)? onItemExpanded;

  /// Currently selected item ID
  final String? selectedItemId;

  /// Currently expanded item ID
  final String? expandedItemId;

  /// Animation duration for expand/collapse
  final Duration animationDuration;

  /// Scroll physics for the list
  final ScrollPhysics? physics;

  /// Whether the list should shrink wrap
  final bool shrinkWrap;

  const ExpandableRewardListWidget({
    required this.items,
    this.onItemSelected,
    this.onItemExpanded,
    this.selectedItemId,
    this.expandedItemId,
    this.animationDuration = const Duration(milliseconds: 300),
    this.physics,
    this.shrinkWrap = false,
    super.key,
  });

  @override
  State<ExpandableRewardListWidget> createState() => _ExpandableRewardListWidgetState();
}

class _ExpandableRewardListWidgetState extends State<ExpandableRewardListWidget> {
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: widget.physics,
      shrinkWrap: widget.shrinkWrap,
      itemCount: widget.items.length,
      itemBuilder: (BuildContext context, int index) {
        final ExpandableRewardListItemModel item = widget.items[index];
        final bool isExpanded = widget.expandedItemId == item.id;
        final bool isSelected = widget.selectedItemId == item.id;

        // Create updated item with current state
        final ExpandableRewardListItemModel updatedItem = item.copyWith(
          isExpanded: isExpanded,
          isSelected: isSelected,
        );

        return ExpandableRewardListItemWidget(
          item: updatedItem,
          animationDuration: widget.animationDuration,
          onTap: () => _handleItemTap(item.id),
          onSelect: () => _handleItemSelect(item.id),
        );
      },
    );
  }

  void _handleItemTap(String itemId) {
    final bool isCurrentlyExpanded = widget.expandedItemId == itemId;

    widget.onItemExpanded?.call(itemId, !isCurrentlyExpanded);
  }

  void _handleItemSelect(String itemId) {
    widget.onItemSelected?.call(itemId);
  }
}

/// Extension to provide convenient methods for working with expandable list items
extension ExpandableRewardListExtension on List<ExpandableRewardListItemModel> {
  /// Find item by ID
  ExpandableRewardListItemModel? findById(String id) {
    try {
      return firstWhere((ExpandableRewardListItemModel item) => item.id == id);
    } on Exception catch (_) {
      return null;
    }
  }

  /// Get all selected items
  List<ExpandableRewardListItemModel> get selectedItems {
    return where((ExpandableRewardListItemModel item) => item.isSelected).toList();
  }

  /// Get all expanded items
  List<ExpandableRewardListItemModel> get expandedItems {
    return where((ExpandableRewardListItemModel item) => item.isExpanded).toList();
  }

  /// Update item by ID
  List<ExpandableRewardListItemModel> updateItem(
    String id,
    ExpandableRewardListItemModel Function(ExpandableRewardListItemModel) updater,
  ) {
    return map((ExpandableRewardListItemModel item) {
      if (item.id == id) {
        return updater(item);
      }
      return item;
    }).toList();
  }

  /// Collapse all items
  List<ExpandableRewardListItemModel> collapseAll() {
    return map((ExpandableRewardListItemModel item) => item.copyWith(isExpanded: false)).toList();
  }

  /// Deselect all items
  List<ExpandableRewardListItemModel> deselectAll() {
    return map((ExpandableRewardListItemModel item) => item.copyWith(isSelected: false)).toList();
  }
}
