/// Model class representing an expandable reward list item
class ExpandableRewardListItemModel {
  /// Unique identifier for the item
  final String id;

  /// Title of the reward category
  final String title;

  /// Description text shown when expanded
  final String description;

  /// List of logo icon asset paths
  final List<String> logoIcons;

  /// Whether this item is currently expanded
  final bool isExpanded;

  /// Whether this item is selected (has radio button checked)
  final bool isSelected;

  const ExpandableRewardListItemModel({
    required this.id,
    required this.title,
    required this.description,
    required this.logoIcons,
    this.isExpanded = false,
    this.isSelected = false,
  });

  /// Creates a copy of this model with updated properties
  ExpandableRewardListItemModel copyWith({
    String? id,
    String? title,
    String? description,
    List<String>? logoIcons,
    bool? isExpanded,
    bool? isSelected,
  }) {
    return ExpandableRewardListItemModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      logoIcons: logoIcons ?? this.logoIcons,
      isExpanded: isExpanded ?? this.isExpanded,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }

    return other is ExpandableRewardListItemModel &&
        other.id == id &&
        other.title == title &&
        other.description == description &&
        other.logoIcons.length == logoIcons.length &&
        other.isExpanded == isExpanded &&
        other.isSelected == isSelected;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      title,
      description,
      logoIcons.length,
      isExpanded,
      isSelected,
    );
  }

  @override
  String toString() {
    return 'ExpandableRewardListItemModel(id: $id, title: $title, isExpanded: $isExpanded, isSelected: $isSelected)';
  }
}
