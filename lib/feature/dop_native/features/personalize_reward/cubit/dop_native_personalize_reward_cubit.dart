import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../data/request/dop_native/dop_native_application_submit_form_request.dart';
import '../../../../../data/response/dop_native/dop_native_metadata_entity.dart';
import '../../../../../data/response/dop_native/dop_native_metadata_item_attribute_entity.dart';
import '../../../../../data/response/dop_native/dop_native_metadata_item_entity.dart';
import '../../../../../data/response/dop_native/dop_native_reward_info_entity.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../resources/dop_native_pdf_url.dart';
import '../../../util/metadata/dop_native_metadata_utils.dart';
import '../../../util/metadata/mock/mock_dop_native_metadata_use_case.dart';
import '../../additional_form/mock/mock_dop_native_application_form_data_use_case.dart';
import 'dop_native_acquisition_reward_state.dart';

class DOPNativeAcquisitionRewardCubit extends CommonCubit<DOPNativeAcquisitionRewardState> {
  final AppState appState;
  final DOPNativeRepo dopNativeRepo;
  final DOPNativeMetadataUtils metadataUtils;

  @visibleForTesting
  List<DOPNativeMetadataItemEntity> entities = <DOPNativeMetadataItemEntity>[];
  @visibleForTesting
  int? selectedIndex;
  @visibleForTesting
  String? termAndConditionPdfUrl;

  String? get leadSource => appState.dopNativeState.dopApplicationState?.flowConfig?.leadSource;

  String? get currentStep => appState.dopNativeState.dopApplicationState?.currentStep;

  DOPNativeAcquisitionRewardCubit({
    required this.appState,
    required this.dopNativeRepo,
    required this.metadataUtils,
  }) : super(DOPNativeAcquisitionRewardInitial());

  void onSelectReward(int index) {
    selectedIndex = index;
    emit(DOPNativeAcquisitionRewardSelected(index));
  }

  Future<void> submitReward() async {
    final int? index = selectedIndex;
    if (index == null) {
      return;
    }
    final DOPNativeMetadataItemEntity selectedEntity = entities[index];
    emit(DOPNativeAcquisitionRewardLoading());
    final DOPNativeApplicationSubmitFormRequest formData = DOPNativeApplicationSubmitFormRequest(
      formAppState: currentStep,
      changeState: true,
      formStep: currentStep,
      formData: ApplicationFormData(
        rewardInfo: DOPNativeRewardInfoEntity(
          rewardID: selectedEntity.code,
        ),
      ),
    );

    final BaseEntity response = await dopNativeRepo.submitApplicationForm(
      form: formData,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeAdditionFormData(
          MockDOPNativeAdditionFormData.submitAcquisitionRewardSuccess,
        ),
      ),
    );

    if (response.statusCode != CommonHttpClient.SUCCESS) {
      emit(DOPNativeAcquisitionRewardError(ErrorUIModel.fromEntity(response)));
      return;
    }
    emit(DOPNativeAcquisitionRewardSubmitSuccess());
  }

  Future<void> openTermAndCondition() async {
    final String? fetchedUrl = termAndConditionPdfUrl;
    // use fetched url for better performance
    if (fetchedUrl != null) {
      emit(DOPNativeAcquisitionRewardTermAndConditionPdfUrlReady(fetchedUrl));
      return;
    }
    // start fetch url when fetched url is null
    emit(DOPNativeAcquisitionRewardLoading());
    final DOPNativeMetadataEntity response =
        await metadataUtils.getAcquisitionRewardTermAndCondition(
      leadSource: leadSource,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeMetadataFileNameByCase(
          MockTestDOPNativeMetadataUseCase.getAcquisitionRewardTermAndConditionSuccess,
        ),
      ),
    );

    if (response.statusCode != CommonHttpClient.SUCCESS) {
      emit(
        DOPNativeAcquisitionRewardError(
          ErrorUIModel(
            statusCode: response.statusCode,
            verdict: response.verdict,
          ),
        ),
      );
      return;
    }
    // create PDF fully URL by concat fileName with env document URL
    final String? fileName = response.metadata?.firstOrNull?.getAttributeByName(
      DOPNativeMetadataItemAttributeEntity.fileName,
    );
    final String pdfUrl = getDOPNativePDFURLByFlavor(fileName ?? '');
    termAndConditionPdfUrl = pdfUrl;
    emit(DOPNativeAcquisitionRewardTermAndConditionPdfUrlReady(pdfUrl));
  }

  Future<void> loadRewards() async {
    emit(DOPNativeAcquisitionRewardLoading());
    final DOPNativeMetadataEntity response = await metadataUtils.getAcquisitionRewards(
      leadSource: leadSource,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeMetadataFileNameByCase(
          MockTestDOPNativeMetadataUseCase.getAcquisitionRewardSuccess,
        ),
      ),
    );

    if (response.statusCode != CommonHttpClient.SUCCESS) {
      emit(
        DOPNativeAcquisitionRewardError(
          ErrorUIModel(
            statusCode: response.statusCode,
            verdict: response.verdict,
          ),
        ),
      );
      return;
    }
    entities = response.metadata ?? <DOPNativeMetadataItemEntity>[];
    emit(DOPNativeAcquisitionRewardLoaded(entities));
  }
}
