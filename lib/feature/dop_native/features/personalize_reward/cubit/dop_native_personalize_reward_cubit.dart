import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../data/response/dop_native/dop_native_personalize_reward_entity.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../util/metadata/dop_native_metadata_utils.dart';
import 'dop_native_personalize_reward_state.dart';

class DOPNativePersonalizeRewardCubit extends CommonCubit<DOPNativePersonalizeRewardState> {
  final AppState appState;
  final DOPNativeRepo dopNativeRepo;
  final DOPNativeMetadataUtils metadataUtils;

  @visibleForTesting
  List<DOPNativeMetadataItemEntity> entities = <DOPNativeMetadataItemEntity>[];

  @visibleForTesting
  int? selectedIndex;

  DOPNativePersonalizeRewardCubit({
    required this.appState,
    required this.dopNativeRepo,
    required this.metadataUtils,
  }) : super(DOPNativePersonalizeRewardInitial());

  void onSelectReward(int index) {
    selectedIndex = index;
    emit(DOPNativePersonalizeRewardSelected(index));
  }

  Future<void> submitReward() async {
    final int? index = selectedIndex;
    if (index == null) {
      return;
    }
    final DOPNativeMetadataItemEntity selectedEntity = entities[index];
    emit(DOPNativePersonalizeRewardLoading());

    //Todo: implement
    // final BaseEntity response =
    //
    // if (response.statusCode != CommonHttpClient.SUCCESS) {
    //   emit(DOPNativePersonalizeRewardError(ErrorUIModel.fromEntity(response)));
    //   return;
    // }

    emit(DOPNativePersonalizeRewardSubmitSuccess());
  }

  Future<void> loadRewards() async {
    emit(DOPNativePersonalizeRewardLoading());
    // final DOPNativeMetadataEntity response = await metadataUtils.getAcquisitionRewards(
    //   leadSource: leadSource,
    //   mockConfig: MockConfig(
    //     enable: false,
    //     fileName: getMockDOPNativeMetadataFileNameByCase(
    //       MockTestDOPNativeMetadataUseCase.getAcquisitionRewardSuccess,
    //     ),
    //   ),
    // );
    //
    // if (response.statusCode != CommonHttpClient.SUCCESS) {
    //   emit(
    //     DOPNativePersonalizeRewardError(
    //       ErrorUIModel(
    //         statusCode: response.statusCode,
    //         verdict: response.verdict,
    //       ),
    //     ),
    //   );
    //   return;
    // }
    // entities = response.metadata ?? <DOPNativeMetadataItemEntity>[];
    // emit(DOPNativePersonalizeRewardLoaded(entities));
  }
}
