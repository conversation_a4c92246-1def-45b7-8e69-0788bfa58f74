import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/response/dop_native/dop_native_personalize_reward_entity.dart';

class DOPNativePersonalizeRewardState implements BlocState {}

class DOPNativePersonalizeRewardInitial extends DOPNativePersonalizeRewardState {}

class DOPNativePersonalizeRewardLoading extends DOPNativePersonalizeRewardState {}

class DOPNativePersonalizeRewardLoaded extends DOPNativePersonalizeRewardState {
  final DOEPersonalizeRewardEntity entity;

  DOPNativePersonalizeRewardLoaded(this.entity);
}

class DOPNativePersonalizeRewardError extends DOPNativePersonalizeRewardState {
  final ErrorUIModel? error;

  DOPNativePersonalizeRewardError(this.error);
}

class DOPNativePersonalizeRewardSelected extends DOPNativePersonalizeRewardState {
  final int selectedIndex;

  DOPNativePersonalizeRewardSelected(this.selectedIndex);
}

class DOPNativePersonalizeRewardSubmitSuccess extends DOPNativePersonalizeRewardState {}
